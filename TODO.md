# TODO: Implement EligibleForEdit Logic Migration from React to Angular

## Information Gathered
- React StatusPill component shows edit actions only if `eligibleForEdit` is true
- When "Edit" is clicked, it opens a modal to select reason for editing
- The modal uses Formik with selectableTags for ReasonToEdit field
- After selecting reason, it calls `handleEditClick` which makes API call to `modifyRequest`
- The API creates a new case and navigates to the appropriate edit page
- Allowance options (Housing, Education, etc.) are shown only if not inflation template
- The logic checks `!(templateId === INFLATION_TEMPLATE_ID_2 || templateId === INFLATION_TEMPLATE_ID)`

## Current Angular Implementation
- Uses `@if(app.EligibleForEdit)` to show actions
- "View/Edit Application" directly calls `viewApplication(app)` which navigates
- Allowance options use `handleEditClick` with parameters for navigation
- No modal for reason selection
- No API call to create new case for editing

## Plan
1. Add edit reason modal to HTML template
2. Add form controls and validation for reason selection
3. Implement API call similar to <PERSON><PERSON>'s `modifyRequest`
4. Update "Edit" action to open modal instead of direct navigation
5. Add logic to handle reason selection and API response
6. Ensure allowance options match React logic
7. Add necessary imports and dependencies

## Dependent Files to be Edited
- `src/app/user/pages/my-social-cases/my-social-cases.component.html` - Add modal template
- `src/app/user/pages/my-social-cases/my-social-cases.component.ts` - Add modal logic, API call, form handling
- `src/app/shared/services/data.service.ts` - Add modifyRequest API method (if needed)

## Followup Steps
- Test the modal functionality
- Verify API integration
- Test navigation after edit
- Ensure translations are available
- Check for any missing dependencies or services
