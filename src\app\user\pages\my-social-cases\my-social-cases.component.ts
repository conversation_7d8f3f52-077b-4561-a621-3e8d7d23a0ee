import { Component, Input, OnInit, QueryList, ViewChildren, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbdSortableHeader, SortEvent } from '../../../shared/directives/sortable.diractive';
import { DataService } from '../../../shared/services/data.service';
import { AlertService } from '../../../shared/services/alert.service';
import { LanguageService } from '../../../shared/services/language.service';
import { AuthService } from '../../../shared/services/auth.service';
import { getValueFromName } from '../../../shared/enums/application-status.enum';
import { ServiceCatalogue } from '../../../shared/enums/service-catalogue.enum';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { finalize } from 'rxjs/operators';



@Component({
  selector: 'app-my-social-cases',
  templateUrl: './my-social-cases.component.html',
  styleUrls: ['./my-social-cases.component.scss']
})
export class MySocialCasesComponent implements OnInit {

  @Input() status: string = '-1';
  @Input() disabled: boolean = true;

  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;
  @ViewChild('newRequestModal') newRequestModal: TemplateRef<any>;
  @ViewChild('editReasonModal') editReasonModal: TemplateRef<any>;

  apps: any;
  userInfo: any;
  statusId = "-1";
  page = 1;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  filteredData: any = [];
  filteredCount: number = 0;
  sortBy = 'CreatedDate';
  sortOrder = 'desc';
  searchTerm = '';

  // Loading and error states
  isLoading = false;
  hasError = false;
  errorMessage = '';

  // Available services for new request modal (matching React)
  availableServices: any[] = [];

  // Edit reason modal properties
  editReasonForm: FormGroup;
  availableReasons: any[] = [];
  selectedApp: any;

  constructor(
    private dataService: DataService,
    private alert: AlertService,
    private router: Router,
    protected lang: LanguageService,
    protected auth: AuthService,
    private modalService: NgbModal,
    private formBuilder: FormBuilder
  ) {
    this.initializeAvailableServices();
    this.initializeEditReasonForm();
  }

  ngOnInit(): void {
    this.statusId = getValueFromName(this.status)?.toString() ?? '-1';
    this.userInfo = this.auth.getUserInfo();
    this.getData();
  }

  initializeEditReasonForm() {
    this.editReasonForm = this.formBuilder.group({
      reason: ['', Validators.required],
      additionalInfo: ['']
    });

    // Initialize available reasons - example reasons, adjust as needed
    this.availableReasons = [
      { id: 1, label: 'Incorrect Information' },
      { id: 2, label: 'Missing Documents' },
      { id: 3, label: 'Other' }
    ];
  }

  openEditReasonModal(app: any) {
    this.selectedApp = app;
    this.editReasonForm.reset();
    this.modalService.open(this.editReasonModal, { size: 'lg', backdrop: 'static' });
  }

  onEditSubmit() {
    if (this.editReasonForm.invalid) {
      this.alert.showMessage('error', 'Please select a reason and provide required information.');
      return;
    }

    const formValue = this.editReasonForm.value;
    // Example: send the reason and additional info to backend or handle accordingly
    console.log('Edit reason submitted for app:', this.selectedApp, formValue);

    // Close modal after submission
    this.modalService.dismissAll();

    // Optionally refresh data or update UI
    this.getData();
  }


  onSort({ column, direction }: SortEvent) {
    this.sortBy = column;
    this.sortOrder = direction;
    this.applyFilters();
  }

  search() {
    this.applyFilters();
  }



  //::TODO Replace static Id with CRM_USER_ID (Heaiba)
  getData() {
    this.isLoading = true;
    this.hasError = false;

    // Use the same approach as React implementation
    const requestBody = {};
    const params: any = {
      _emirateId: this.userInfo?.userInfo?.idn
    };

    // Add comprehensive query parameters
    if (this.statusId && this.statusId !== '-1') {
      params['statusId'] = this.statusId;
    }

    // Add pagination parameters
    params['page'] = this.page;
    params['pageSize'] = this.pageSize;

    // Add sorting parameters
    params['sortColumn'] = this.sortBy;
    params['sortDirection'] = this.sortOrder;

    // Add search term if provided
    if (this.searchTerm && this.searchTerm.trim()) {
      params['searchTerm'] = this.searchTerm.trim();
    }

    // Convert params to query string for the URL
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    this.dataService
      .post(`swpproxy/Request/RetrieveAllRequests?${queryString}`, requestBody)
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (res) => {
          if (res && (res.Data || res.data)) {
            // Handle both Data and data response formats
            this.filteredData = res.Data || res.data;
            this.filteredCount = res.dataCount || res.DataCount || this.filteredData.length;
            this.hasError = false;

            // Sort data by creation date (newest first) like in React version
            if (this.filteredData && Array.isArray(this.filteredData)) {
              this.filteredData.sort((a, b) =>
                new Date(b.CreatedDate).getTime() - new Date(a.CreatedDate).getTime()
              );
            }
          } else {
            this.filteredData = [];
            this.filteredCount = 0;
          }
        },
        error: (error) => {
          console.error('Error fetching data:', error);
          this.hasError = true;
          this.errorMessage = 'Failed to load applications. Please try again.';
          this.filteredData = [];
          this.filteredCount = 0;
          this.alert.showMessage('error', 'Failed to load applications. Please try again.');
        }
      });
  }

  async deleteApplication(id: string, entityName: string) {
    const confirmed = await this.alert.confirmSubmit('');
    if (confirmed) {
      this.dataService
        .delete(`Global/DeleteDraftRequest?requestId=${id}&entityName=${entityName}`)
        .subscribe(() => {
          this.applyFilters();
        });
    }
  }

  viewApplication(app: any) {
    switch (app.ServiceCatalogue.toLocaleLowerCase()) {
      case ServiceCatalogue.LostCard.toLocaleLowerCase():
        this.router.navigate(['/e-services/pod-lost-card', app.Id]);
        break;
      case ServiceCatalogue.IssueCard.toLocaleLowerCase():
        this.router.navigate(['/e-services/pod-new-card', app.Id]);
        break;
      case ServiceCatalogue.MarriageGrant.toLowerCase():
        this.router.navigate(['/e-services/marriage-grant', app.Id]);
        break;
      case ServiceCatalogue.MassWedding.toLowerCase():
        this.router.navigate(['/e-services/mass-wedding', app.Id]);
        break;
      // case 'fd3677ba-35d5-ee11-b10d-005056010908':
      //   this.router.navigate(['/e-services/npo-declaration', app.Id]);
      //   break;
      case ServiceCatalogue.PodCenterRegistration.toLowerCase():
        this.router.navigate(['/e-services/pod-centers', app.Id]);
        break;
      case ServiceCatalogue.NPOLicenseDeclaration:
      case ServiceCatalogue.NPOLicenseDeclarationByDecree:
        this.router.navigate(['/e-services/npo-license-declaration/npo-request-details/', app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue }
        });
        break;
      case ServiceCatalogue.EstablishmentEdit:
        this.router.navigate(['/e-services/npo-license-declaration/npo-request-details/', app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue, establishmentId: app.EstablishmentID, serviceCatalogueName: app.ServiceCatalogueName, serviceCatalogueNameAR: app.ServiceCatalogueNameAR }
        });
        // window.location.href = `/user-pages/my-establishments/details/${app.EstablishmentID}/2/${app.Id}`
        break;
      case ServiceCatalogue.IssueLicenseNonMuslimWorshipPlace:
        this.router.navigate(['/e-services/non-muslims-worship-place-license/details/', app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue }
        });
        break;
      case ServiceCatalogue.RequestAllocationWorshipRoom:
        this.router.navigate(['/e-services/non-muslims-worship-place-license/room-details/', app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue }
        });
        break;
      case ServiceCatalogue.RequestToIssueFundraisingPermit:
        this.router.navigate([`/e-services/fund-raising-service/details/${app.EstablishmentID}/`, app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue }
        });
        break;
      case ServiceCatalogue.AffiliateSubscribeOrJoinAssociationsOrRegionalInternationalEntities:
        this.router.navigate([`/e-services/joining-and-affiliating-associations/details/${app.EstablishmentID}/`, app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue }
        });
        break;
      case ServiceCatalogue.ParticipateInActivitiesAndEvents:
        this.router.navigate([`/e-services/activities-and-events-participation/details/${app.EstablishmentID}/`, app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue }
        });
        break;
      case ServiceCatalogue.OrganizeActivitiesAndEvents:
        this.router.navigate([`/e-services/organizing-events-and-activities/details/${app.EstablishmentID}/`, app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue }
        });
        break;

      case ServiceCatalogue.RequestToExtendFundraisingPermit:
        this.router.navigate([`/e-services/fund-raising-service/extend-details/${app.EstablishmentID}/`, app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue }
        });
        break;


      case ServiceCatalogue.RequestForApprovalOfOpeningNPOBranch:
        this.router.navigate([`/e-services/request-for-approval-of-opening-npo-branch/details/${app.EstablishmentID}/`, app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue }
        });
        break;

      case ServiceCatalogue.RequestForReceiveDonationsNOC:
        this.router.navigate([`/e-services/noc-request/details/${app.EstablishmentID}/`, app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue }
        });
        break;

      case ServiceCatalogue.OpeningNewBankAccountRequest:
        this.router.navigate([`/e-services/request-to-open-new-bank-account-certificate/details/${app.EstablishmentID}/`, app.Id], {
          queryParams: { serviceCatalouge: app.ServiceCatalogue }
        });
        break;



      default:
        break;
    }

  }


  get ServiceCatalogue() {
    return ServiceCatalogue;
  }

  onFilterChange() {
    this.applyFilters();
  }
  onPageChange() {
    this.applyFilters();
  }
  onSelectChange() {
    this.applyFilters();
  }
  onStatusChange() {
    this.applyFilters();
  }



  // Initialize available services for new request modal
  initializeAvailableServices() {
    this.availableServices = [
      {
        name: 'Social Aid Application',
        nameAr: 'طلب الدعم الاجتماعي',
        link: '/e-services/social-aid',
        description: 'Apply for social welfare support'
      },
      {
        name: 'To Whom It May Concern',
        nameAr: 'لمن يهمه الأمر',
        link: '/e-services/to-whom-apply',
        description: 'Request official certificate'
      },
      {
        name: 'Complaint',
        nameAr: 'شكوى',
        link: '/e-services/complaint',
        description: 'Submit a complaint or inquiry'
      }
    ];
  }

  // Open new request modal
  openNewRequestModal() {
    if (this.newRequestModal) {
      this.modalService.open(this.newRequestModal, { size: 'lg', backdrop: 'static' });
    }
  }

  // Navigate to service
  navigateToService(serviceLink: string) {
    this.modalService.dismissAll();
    this.router.navigate([serviceLink]);
  }

  // Handle service card hover effects
  onServiceCardHover(event: Event, isHover: boolean) {
    const target = event.target as HTMLElement;
    if (target) {
      target.style.backgroundColor = isHover ? '#f8f9fa' : 'white';
    }
  }

  // Check if application is inflation template (to hide additional allowance options)
  // This matches the exact logic from React StatusPill component
  isInflationTemplate(app: any): boolean {
    const INFLATION_TEMPLATE_ID = '94b2a9e5-d2ae-ee11-a568-000d3a6c23a9';
    const INFLATION_TEMPLATE_ID_2 = 'c1b5dedd-62e8-40a6-9a3a-997355dda8ec';

    // Exact match from React: templateId === INFLATION_TEMPLATE_ID_2 || templateId === INFLATION_TEMPLATE_ID
    return app.TemplateId === INFLATION_TEMPLATE_ID_2 || app.TemplateId === INFLATION_TEMPLATE_ID;
  }

  // Handle edit click for different allowance types
  // This matches the exact parameter structure from React StatusPill: (e, isHousingEducationTopup, isHousingTopup, isEducationTopup)
  handleEditClick(event: Event, app: any, isHousingEducationTopup: boolean = false, isHousingTopup: boolean = false, isEducationTopup: boolean = false) {
    event.stopPropagation();

    console.log('Handle edit click:', {
      app,
      isHousingEducationTopup,
      isHousingTopup,
      isEducationTopup
    });

    // Navigate to the appropriate edit page with additional parameters
    if (app.CaseId) {
      const navigationExtras: any = {
        queryParams: {}
      };

      // Add query parameters based on the type of allowance being applied for
      if (isHousingEducationTopup) {
        navigationExtras.queryParams.applyHousingEducation = 'true';
      } else if (isHousingTopup) {
        navigationExtras.queryParams.applyHousing = 'true';
      } else if (isEducationTopup) {
        navigationExtras.queryParams.applyEducation = 'true';
      }

      // Navigate based on the service type
      switch (app.ServiceCatalogue?.toLowerCase()) {
        case 'social-aid':
        case 'socialaid':
          this.router.navigate(['/e-services/social-aid', app.CaseId], navigationExtras);
          break;
        default:
          // Fallback to general edit route
          this.router.navigate(['/e-services/edit-request', app.CaseId], navigationExtras);
          break;
      }
    } else {
      console.warn('No CaseId found for application');
      this.alert.showMessage('error', 'Unable to process request. Case ID not found.');
    }
  }





  // Page size change handler
  onPageSizeChange() {
    this.page = 1; // Reset to first page
    this.applyFilters();
  }

  // Get status display information
  getStatusInfo(status: any) {
    if (!status) return { color: '#C3C6CB', label: 'Unknown' };

    const statusValue = status.value || status.Value || status;

    switch (statusValue) {
      case 'Draft':
      case 1:
        return { color: '#C3C6CB', label: this.lang.IsArabic ? 'مسودة' : 'Draft' };
      case 'Submitted':
      case 100000000:
        return { color: '#4A9D5C', label: this.lang.IsArabic ? 'مقدم' : 'Submitted' };
      case 'In Progress':
      case 100000001:
        return { color: '#F8C027', label: this.lang.IsArabic ? 'قيد الإجراء' : 'In Progress' };
      case 'Returned':
      case 100000002:
        return { color: '#D83731', label: this.lang.IsArabic ? 'تم الإرجاع' : 'Returned' };
      case 'Approved':
      case 100000003:
        return { color: '#4A9D5C', label: this.lang.IsArabic ? 'موافقة' : 'Approved' };
      case 'Rejected':
      case 100000004:
        return { color: '#eb0505', label: this.lang.IsArabic ? 'مرفوض' : 'Rejected' };
      case 'Pending Payment':
      case 100000005:
        return { color: '#286CFF', label: this.lang.IsArabic ? 'بإنتظار الدفع' : 'Pending Payment' };
      default:
        return { color: '#C3C6CB', label: statusValue };
    }
  }

  // Enhanced search
  onSearchInput(event: any) {
    this.searchTerm = event.target.value;
    this.page = 1; // Reset to first page
    this.applyFilters();
  }

  // Refresh data
  refreshData() {
    this.getData();
  }

  // Apply filters and refresh data
  applyFilters() {
    this.page = 1; // Reset to first page when applying filters
    this.getData();
  }
}
